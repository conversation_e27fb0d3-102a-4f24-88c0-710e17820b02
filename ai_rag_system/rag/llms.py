from llama_index.llms.openai import OpenAI

from ai_rag_system.rag.config import RagConfig


def create_gitee_llm(api_key, model, **kwargs):
    """创建 Gitee 平台的 LLM 实例"""
    llm = OpenAI(api_key=api_key,
                 model=model,
                 api_base="https://ai.gitee.com/v1",
                 **kwargs)
    return llm


def create_fastgpt_llm(api_key, model, **kwargs):
    """创建 FastGPT 平台的 LLM 实例"""
    llm = OpenAI(api_key=api_key,
                 model=model,
                 api_base="https://api.fastgpt.in/api/v1",
                 **kwargs)
    return llm


def gitee_deepseek_v3_llm(**kwargs):
    return create_gitee_llm(
        api_key="RWXIXI17C22UTQJEMVCI2AZEWGVOPE493Q2GM31A",
        model="DeepSeek-V3",
        **kwargs
    )


def gitee_kimi_k2_llm(**kwargs):
    return create_gitee_llm(
        api_key="RWXIXI17C22UTQJEMVCI2AZEWGVOPE493Q2GM31A",
        model="kimi-k2-instruct",
        **kwargs
    )


def fastGPT_claude_llm(**kwargs):
    return create_fastgpt_llm(
        api_key="fastgpt-rFP76EYk5wkZ1S9va6n90mhT7ragq777EmXZ8TaanGQCicZuZcjbpxmXGZC5tg4Zv",
        model="claude-3-5-haiku-20241022",
        **kwargs
    )

def moonshot_llm(**kwargs):
    llm = OpenAI(api_key=RagConfig.moonshot_api_key,
                 base_url="https://api.moonshot.cn/v1",
                 **kwargs)
    return llm