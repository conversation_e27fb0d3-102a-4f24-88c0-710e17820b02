# chainlit create-secret
CHAINLIT_AUTH_SECRET="hj%J?i*=po7SA_/XW_H^6X@4_hVeGzjct0rM5xbfmkJ^$$,K.*u=.wq,o9L%9Rc6"
API_BASE="https://ai.gitee.com/v1"
API_KEY="RWXIXI17C22UTQJEMVCI2AZEWGVOPE493Q2GM31A"
MODEL_NAME="gitee_deepseek_v3_llm"

MINIO_ENDPOINT="*************:9000"
MINIO_ACCESS_KEY="minioadmin"
MINIO_SECRET_KEY="minioadmin"
MINIO_BUCKET_NAME="005"
#代码访问是9000，浏览器访问是9001
# MinIO 社区版 默认只有一组“根凭证”，即启动时通过环境变量指定的：
# MINIO的 Access Key和 Secret Key，默认是：minioadmin/minioadmin
# MinIO 提供了一个强大的命令行工具 mc（MinIO Client），可以通过它来修改桶的访问权限。

CONNECTION_STRING="postgresql+asyncpg://postgres:rxd123rxd@localhost/chainlit_db"

MILVUS_URI="http://**************:19530"

MOONSHOT_API_KEY="sk-dONPz6CqbSzNHMGX6fhwUaSqrWi9RHRkp1XAx51acXwPhnNk"

